/** @format */

'use client';

import React, { useEffect, useState } from 'react';

interface FilterOptions {
	categories: string[];
	subcategories: string[];
	cities: string[];
	districts: string[];
	neighborhoods: string[];
	countries: string[];
}

interface SubcategoryItem {
	subcategory: string;
	count: number;
}

interface POISimpleFilterProps {
	onFiltersChange: (filters: {
		searchTerm?: string;
		category?: string;
		subcategory?: string;
		city?: string;
		district?: string;
		neighborhood?: string;
		country?: string;
	}) => void;
	className?: string;
	showSearch?: boolean;
	showCategory?: boolean;
	showLocation?: boolean;
	layout?: 'horizontal' | 'vertical' | 'compact';
	placeholder?: string;
}

const POISimpleFilter: React.FC<POISimpleFilterProps> = ({
	onFiltersChange,
	className = '',
	showSearch = true,
	showCategory = true,
	showLocation = true,
	layout = 'horizontal',
	placeholder = 'Search POIs by name or ID...',
}) => {
	const [searchTerm, setSearchTerm] = useState('');
	const [category, setCategory] = useState('');
	const [subcategory, setSubcategory] = useState('');
	const [city, setCity] = useState('');
	const [district, setDistrict] = useState('');
	const [neighborhood, setNeighborhood] = useState('');
	const [country, setCountry] = useState('');

	const [filterOptions, setFilterOptions] = useState<FilterOptions>({
		categories: [],
		subcategories: [],
		cities: [],
		districts: [],
		neighborhoods: [],
		countries: [],
	});

	const [loading, setLoading] = useState(false);

	// Load initial filter options
	useEffect(() => {
		loadFilterOptions();
	}, []);

	// Load subcategories when category changes
	useEffect(() => {
		if (category) {
			loadSubcategories(category);
			setSubcategory(''); // Reset subcategory when category changes
		} else {
			loadSubcategories(); // Load all subcategories when no category selected
			setSubcategory('');
		}
	}, [category]);

	// Reset child location filters when parent changes
	useEffect(() => {
		setDistrict('');
		setNeighborhood('');
	}, [city]);

	useEffect(() => {
		setNeighborhood('');
	}, [district]);

	// Notify parent of filter changes
	useEffect(() => {
		onFiltersChange({
			searchTerm: searchTerm || undefined,
			category: category || undefined,
			subcategory: subcategory || undefined,
			city: city || undefined,
			district: district || undefined,
			neighborhood: neighborhood || undefined,
			country: country || undefined,
		});
	}, [
		searchTerm,
		category,
		subcategory,
		city,
		district,
		neighborhood,
		country,
		onFiltersChange,
	]);

	const loadFilterOptions = async () => {
		setLoading(true);
		try {
			const response = await fetch('/api/pois/filter-options');
			const data = await response.json();

			if (data.success) {
				setFilterOptions({
					categories: data.categories || [],
					subcategories: data.subcategories || [],
					cities: data.cities || [],
					districts: data.districts || [],
					neighborhoods: data.neighborhoods || [],
					countries: data.countries || [],
				});
			}
		} catch (error) {
			console.error('Failed to load filter options:', error);
		} finally {
			setLoading(false);
		}
	};

	const loadSubcategories = async (selectedCategory?: string) => {
		try {
			const url = selectedCategory
				? `/api/pois/subcategories?category=${encodeURIComponent(
						selectedCategory
				  )}`
				: '/api/pois/subcategories';

			const response = await fetch(url);
			const data = await response.json();

			if (data.success) {
				setFilterOptions((prev) => ({
					...prev,
					subcategories: data.subcategories.map(
						(item: SubcategoryItem) => item.subcategory
					),
				}));
			}
		} catch (error) {
			console.error('Failed to load subcategories:', error);
		}
	};

	const loadDistricts = async (selectedCity: string) => {
		try {
			const response = await fetch(
				`/api/pois/suggestions?field=district&city=${encodeURIComponent(
					selectedCity
				)}`
			);
			const data = await response.json();

			if (data.success) {
				setFilterOptions((prev) => ({
					...prev,
					districts: data.districts || [],
				}));
			}
		} catch (error) {
			console.error('Failed to load districts:', error);
		}
	};

	const loadNeighborhoods = async (
		selectedCity: string,
		selectedDistrict: string
	) => {
		try {
			const response = await fetch(
				`/api/pois/suggestions?field=neighborhood&city=${encodeURIComponent(
					selectedCity
				)}&district=${encodeURIComponent(selectedDistrict)}`
			);
			const data = await response.json();

			if (data.success) {
				setFilterOptions((prev) => ({
					...prev,
					neighborhoods: data.neighborhoods || [],
				}));
			}
		} catch (error) {
			console.error('Failed to load neighborhoods:', error);
		}
	};

	const handleCityChange = (newCity: string) => {
		setCity(newCity);
		if (newCity) {
			loadDistricts(newCity);
		} else {
			setFilterOptions((prev) => ({
				...prev,
				districts: [],
				neighborhoods: [],
			}));
		}
	};

	const handleDistrictChange = (newDistrict: string) => {
		setDistrict(newDistrict);
		if (newDistrict && city) {
			loadNeighborhoods(city, newDistrict);
		} else {
			setFilterOptions((prev) => ({ ...prev, neighborhoods: [] }));
		}
	};

	const clearAllFilters = () => {
		setSearchTerm('');
		setCategory('');
		setSubcategory('');
		setCity('');
		setDistrict('');
		setNeighborhood('');
		setCountry('');
	};

	const hasActiveFilters =
		searchTerm ||
		category ||
		subcategory ||
		city ||
		district ||
		neighborhood ||
		country;

	const containerClass =
		layout === 'vertical'
			? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'
			: layout === 'compact'
			? 'flex flex-col lg:flex-row gap-6'
			: 'flex flex-wrap gap-3';

	// Compact layout with everything in a single row
	if (layout === 'compact') {
		return (
			<div className={`${className}`}>
				{/* True Single Row with Search and All Filters */}
				<div className='flex gap-2 items-center overflow-x-auto'>
					{/* Search Input */}
					{showSearch && (
						<div className='relative flex-shrink-0'>
							<input
								type='text'
								placeholder={placeholder}
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className='pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-xs font-medium'
								style={{
									borderColor: '#D1D5DB',
									backgroundColor: '#F9FAFB',
									color: '#374151',
									width: '180px',
								}}
							/>
							<div className='absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none'>
								<svg
									className='h-4 w-4'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'
									style={{ color: '#6B7280' }}>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={2}
										d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'
									/>
								</svg>
							</div>
							{searchTerm && (
								<button
									onClick={() => setSearchTerm('')}
									className='absolute inset-y-0 right-0 pr-2 flex items-center hover:opacity-70 transition-opacity duration-200'>
									<svg
										className='h-3 w-3'
										fill='none'
										stroke='currentColor'
										viewBox='0 0 24 24'
										style={{ color: '#6B7280' }}>
										<path
											strokeLinecap='round'
											strokeLinejoin='round'
											strokeWidth={2}
											d='M6 18L18 6M6 6l12 12'
										/>
									</svg>
								</button>
							)}
						</div>
					)}

					{/* Category Filter */}
					{showCategory && (
						<select
							value={category}
							onChange={(e) => setCategory(e.target.value)}
							className='px-2 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-xs transition-all duration-300 font-medium flex-shrink-0'
							style={{
								borderColor: '#D1D5DB',
								color: '#374151',
								backgroundColor: '#F9FAFB',
								width: 'auto',
								minWidth: 'fit-content',
							}}
							disabled={loading}>
							<option value=''>Category</option>
							{filterOptions.categories.map((cat) => (
								<option
									key={cat}
									value={cat}>
									{cat}
								</option>
							))}
						</select>
					)}

					{/* Subcategory Filter */}
					{showCategory && (
						<select
							value={subcategory}
							onChange={(e) => setSubcategory(e.target.value)}
							className='px-2 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-xs transition-all duration-300 font-medium flex-shrink-0'
							style={{
								borderColor: '#D1D5DB',
								color: '#374151',
								backgroundColor: '#F9FAFB',
								width: 'auto',
								minWidth: 'fit-content',
							}}
							disabled={loading || filterOptions.subcategories.length === 0}>
							<option value=''>Subcategory</option>
							{filterOptions.subcategories.map((subcat) => (
								<option
									key={subcat}
									value={subcat}>
									{subcat}
								</option>
							))}
						</select>
					)}

					{/* Country Filter */}
					{showLocation && (
						<select
							value={country}
							onChange={(e) => setCountry(e.target.value)}
							className='px-2 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-xs transition-all duration-300 font-medium flex-shrink-0'
							style={{
								borderColor: '#D1D5DB',
								color: '#374151',
								backgroundColor: '#F9FAFB',
								width: 'auto',
								minWidth: 'fit-content',
							}}
							disabled={loading}>
							<option value=''>Country</option>
							{filterOptions.countries.map((countryOption) => (
								<option
									key={countryOption}
									value={countryOption}>
									{countryOption}
								</option>
							))}
						</select>
					)}

					{/* City Filter */}
					{showLocation && (
						<select
							value={city}
							onChange={(e) => handleCityChange(e.target.value)}
							className='px-2 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-xs transition-all duration-300 font-medium flex-shrink-0'
							style={{
								borderColor: '#D1D5DB',
								color: '#374151',
								backgroundColor: '#F9FAFB',
								width: 'auto',
								minWidth: 'fit-content',
							}}
							disabled={loading}>
							<option value=''>City</option>
							{filterOptions.cities.map((cityOption) => (
								<option
									key={cityOption}
									value={cityOption}>
									{cityOption}
								</option>
							))}
						</select>
					)}

					{/* District Filter */}
					{showLocation && (
						<select
							value={district}
							onChange={(e) => handleDistrictChange(e.target.value)}
							className='px-2 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-xs transition-all duration-300 font-medium flex-shrink-0'
							style={{
								borderColor: '#D1D5DB',
								color: '#374151',
								backgroundColor: '#F9FAFB',
								width: 'auto',
								minWidth: 'fit-content',
							}}
							disabled={
								loading || !city || filterOptions.districts.length === 0
							}>
							<option value=''>District</option>
							{filterOptions.districts.map((districtOption) => (
								<option
									key={districtOption}
									value={districtOption}>
									{districtOption}
								</option>
							))}
						</select>
					)}

					{/* Neighborhood Filter */}
					{showLocation && filterOptions.neighborhoods.length > 0 && (
						<select
							value={neighborhood}
							onChange={(e) => setNeighborhood(e.target.value)}
							className='px-2 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-xs transition-all duration-300 font-medium flex-shrink-0'
							style={{
								borderColor: '#D1D5DB',
								color: '#374151',
								backgroundColor: '#F9FAFB',
								width: 'auto',
								minWidth: 'fit-content',
							}}
							disabled={loading || !district}>
							<option value=''>Neighborhood</option>
							{filterOptions.neighborhoods.map((neighborhoodOption) => (
								<option
									key={neighborhoodOption}
									value={neighborhoodOption}>
									{neighborhoodOption}
								</option>
							))}
						</select>
					)}

					{/* Clear All Button */}
					<button
						onClick={clearAllFilters}
						className='px-3 py-2 rounded-lg transition-all duration-300 text-xs font-medium whitespace-nowrap hover:shadow-md transform hover:-translate-y-0.5 flex-shrink-0'
						style={{
							background: '#EF4444',
							color: '#FFFFFF',
							border: '1px solid #DC2626',
						}}>
						Clear
					</button>
				</div>
			</div>
		);
	}

	// Original layout for vertical and horizontal
	return (
		<div className={`${className}`}>
			{showSearch && (
				<div className='relative mb-6'>
					<input
						type='text'
						placeholder={placeholder}
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className='w-full pl-12 pr-4 py-4 border rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-lg'
						style={{
							borderColor: '#E5E7EB', // colors.ui.gray200
							backgroundColor: '#F9FAFB', // colors.ui.gray50
						}}
					/>
					<div className='absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none'>
						<svg
							className='h-6 w-6'
							fill='none'
							stroke='currentColor'
							viewBox='0 0 24 24'
							style={{ color: '#5E6E7E' }}>
							{' '}
							{/* colors.neutral.slateGray */}
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={2}
								d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'
							/>
						</svg>
					</div>
					{searchTerm && (
						<button
							onClick={() => setSearchTerm('')}
							className='absolute inset-y-0 right-0 pr-4 flex items-center hover:opacity-70 transition-opacity duration-200'>
							<svg
								className='h-5 w-5'
								fill='none'
								stroke='currentColor'
								viewBox='0 0 24 24'
								style={{ color: '#5E6E7E' }}>
								{' '}
								{/* colors.neutral.slateGray */}
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth={2}
									d='M6 18L18 6M6 6l12 12'
								/>
							</svg>
						</button>
					)}
				</div>
			)}

			<div className={containerClass}>
				{showCategory && (
					<>
						{/* Category Filter */}
						<select
							value={category}
							onChange={(e) => setCategory(e.target.value)}
							className='px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm transition-all duration-300 font-medium'
							style={{
								borderColor: '#E5E7EB', // colors.ui.gray200
								color: '#1B1B1E', // colors.neutral.textBlack
								backgroundColor: '#F9FAFB', // colors.ui.gray50
							}}
							disabled={loading}>
							<option value=''>All Categories</option>
							{filterOptions.categories.map((cat) => (
								<option
									key={cat}
									value={cat}>
									{cat}
								</option>
							))}
						</select>

						{/* Subcategory Filter */}
						<select
							value={subcategory}
							onChange={(e) => setSubcategory(e.target.value)}
							className='px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm transition-all duration-300 font-medium'
							style={{
								borderColor: '#E5E7EB', // colors.ui.gray200
								color: '#1B1B1E', // colors.neutral.textBlack
								backgroundColor: '#F9FAFB', // colors.ui.gray50
							}}
							disabled={loading || filterOptions.subcategories.length === 0}>
							<option value=''>All Subcategories</option>
							{filterOptions.subcategories.map((subcat) => (
								<option
									key={subcat}
									value={subcat}>
									{subcat}
								</option>
							))}
						</select>
					</>
				)}

				{showLocation && (
					<>
						{/* Country Filter */}
						<select
							value={country}
							onChange={(e) => setCountry(e.target.value)}
							className='px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm transition-all duration-300 font-medium'
							style={{
								borderColor: '#E5E7EB', // colors.ui.gray200
								color: '#1B1B1E', // colors.neutral.textBlack
								backgroundColor: '#F9FAFB', // colors.ui.gray50
							}}
							disabled={loading}>
							<option value=''>All Countries</option>
							{filterOptions.countries.map((countryOption) => (
								<option
									key={countryOption}
									value={countryOption}>
									{countryOption}
								</option>
							))}
						</select>

						{/* City Filter */}
						<select
							value={city}
							onChange={(e) => handleCityChange(e.target.value)}
							className='px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm transition-all duration-300 font-medium'
							style={{
								borderColor: '#E5E7EB', // colors.ui.gray200
								color: '#1B1B1E', // colors.neutral.textBlack
								backgroundColor: '#F9FAFB', // colors.ui.gray50
							}}
							disabled={loading}>
							<option value=''>All Cities</option>
							{filterOptions.cities.map((cityOption) => (
								<option
									key={cityOption}
									value={cityOption}>
									{cityOption}
								</option>
							))}
						</select>

						{/* District Filter */}
						<select
							value={district}
							onChange={(e) => handleDistrictChange(e.target.value)}
							className='px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm transition-all duration-300 font-medium'
							style={{
								borderColor: '#E5E7EB', // colors.ui.gray200
								color: '#1B1B1E', // colors.neutral.textBlack
								backgroundColor: '#F9FAFB', // colors.ui.gray50
							}}
							disabled={
								loading || !city || filterOptions.districts.length === 0
							}>
							<option value=''>All Districts</option>
							{filterOptions.districts.map((districtOption) => (
								<option
									key={districtOption}
									value={districtOption}>
									{districtOption}
								</option>
							))}
						</select>

						{/* Neighborhood Filter */}
						{filterOptions.neighborhoods.length > 0 && (
							<select
								value={neighborhood}
								onChange={(e) => setNeighborhood(e.target.value)}
								className='px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm transition-all duration-300 font-medium'
								style={{
									borderColor: '#E5E7EB', // colors.ui.gray200
									color: '#1B1B1E', // colors.neutral.textBlack
									backgroundColor: '#F9FAFB', // colors.ui.gray50
								}}
								disabled={loading || !district}>
								<option value=''>All Neighborhoods</option>
								{filterOptions.neighborhoods.map((neighborhoodOption) => (
									<option
										key={neighborhoodOption}
										value={neighborhoodOption}>
										{neighborhoodOption}
									</option>
								))}
							</select>
						)}
					</>
				)}

				{/* Clear Filters Button */}
				{hasActiveFilters && (
					<button
						onClick={clearAllFilters}
						className='px-6 py-3 rounded-xl transition-all duration-300 text-sm font-medium whitespace-nowrap hover:shadow-lg transform hover:-translate-y-0.5'
						style={{
							background: `linear-gradient(135deg, #F3F4F6 0%, #E5E7EB 100%)`, // colors.ui.gray100 to gray200
							color: '#5E6E7E', // colors.neutral.slateGray
							border: '1px solid #E5E7EB', // colors.ui.gray200
						}}>
						✨ Clear All
					</button>
				)}
			</div>
		</div>
	);
};

export default POISimpleFilter;
